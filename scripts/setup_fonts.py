#!/usr/bin/env python3
"""
Typst/typst.ts CJK 字体安装与配置脚本（经校正）
- 安装 Noto CJK（优先 TTC/OTF，避免 VF 包）
- 为 Typst/typst-ts-cli 配置字体搜索路径
- 生成带中英混排与 CJK 间距设置的中文模板
"""
import os, sys, shutil, subprocess, tempfile, platform
from pathlib import Path

HERE = Path(__file__).resolve().parent
PROJECT_ROOT = HERE.parent
FONTS_DIR = PROJECT_ROOT / "fonts"
TEMP_FONTS_DIR = Path(tempfile.gettempdir()) / "typst-fonts"

def run(cmd: list[str], cwd: Path | None = None) -> bool:
    try:
        subprocess.run(cmd, cwd=cwd, check=True, text=True, capture_output=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {' '.join(cmd)}\n{e.stderr.strip()}")
        return False

def sudo_prefix() -> list[str]:
    # 仅在需要且有 sudo 时添加
    if os.geteuid() != 0 and shutil.which("sudo"):
        return ["sudo"]
    return []

def install_system_fonts() -> None:
    print("📦 安装 Noto CJK 字体 …")
    sysname = platform.system().lower()

    if shutil.which("apt-get"):
        cmds = [
            sudo_prefix() + ["apt-get", "update"],
            sudo_prefix() + ["apt-get", "install", "-y", "fonts-noto-cjk"],
        ]  # Ubuntu/Debian 官方包名。:contentReference[oaicite:6]{index=6}
    elif shutil.which("dnf") or shutil.which("yum"):
        pm = "dnf" if shutil.which("dnf") else "yum"
        # Fedora/RHEL：安装 TTC 包（非 *-vf 变量字体）
        cmds = [
            sudo_prefix() + [pm, "install", "-y", "google-noto-sans-cjk-ttc-fonts"],
            sudo_prefix() + [pm, "install", "-y", "google-noto-serif-cjk-ttc-fonts"],
        ]  # :contentReference[oaicite:7]{index=7}
    elif shutil.which("apk"):
        cmds = [sudo_prefix() + ["apk", "add", "--no-cache", "font-noto-cjk"]]  # Alpine。:contentReference[oaicite:8]{index=8}
    elif sysname == "darwin" and shutil.which("brew"):
        # macOS：建议按地区变体安装 SC
        cmds = [
            ["brew", "tap", "homebrew/cask-fonts"],
            ["brew", "install", "--cask", "font-noto-sans-cjk-sc"],
            ["brew", "install", "--cask", "font-noto-serif-cjk-sc"],
        ]  # :contentReference[oaicite:9]{index=9}
    else:
        print("⚠️ 未检测到受支持的包管理器；将尝试直接下载 OTF。")
        cmds = []

    ok = True
    for c in cmds:
        ok &= run(c)
    if ok:
        print("✅ 系统字体安装完成")

def ensure_dirs():
    FONTS_DIR.mkdir(parents=True, exist_ok=True)
    TEMP_FONTS_DIR.mkdir(parents=True, exist_ok=True)

def try_copy_system_fonts():
    print("📁 尝试复制系统已有的 Noto CJK 字体 …")
    candidates = [
        "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
        "/usr/share/fonts/opentype/noto/NotoSerifCJK-Regular.ttc",
        "/usr/share/fonts/truetype/noto/NotoSansCJKsc-Regular.otf",
        "/usr/share/fonts/truetype/noto/NotoSerifCJKsc-Regular.otf",
        # macOS（部分通过手动安装或 Homebrew）
        "/Library/Fonts/NotoSansCJKsc-Regular.otf",
        "/Library/Fonts/NotoSerifCJKsc-Regular.otf",
    ]  # TTC/OTF 文件名来源于官方仓库。:contentReference[oaicite:10]{index=10}

    found = False
    for p in candidates:
        if Path(p).exists():
            for target in (FONTS_DIR, TEMP_FONTS_DIR):
                shutil.copy2(p, target / Path(p).name)
            print(f"✅ 复制 {p}")
            found = True
    return found

def download_otf_backup():
    print("🌐 下载官方 OTF（SC 变体）……")
    # 直接引用 notofonts/noto-cjk 仓库的 OTF 路径（大文件，需网络）
    urls = {
        "NotoSansCJKsc-Regular.otf":
            "https://github.com/notofonts/noto-cjk/raw/main/Sans/OTF/SimplifiedChinese/NotoSansCJKsc-Regular.otf",
        "NotoSerifCJKsc-Regular.otf":
            "https://github.com/notofonts/noto-cjk/raw/main/Serif/OTF/SimplifiedChinese/NotoSerifCJKsc-Regular.otf",
    }  # 路径见官方仓库结构。:contentReference[oaicite:11]{index=11}
    try:
        import urllib.request
        for fname, url in urls.items():
            dest = FONTS_DIR / fname
            if not dest.exists():
                print(f"  ↓ {fname}")
                urllib.request.urlretrieve(url, dest)
                shutil.copy2(dest, TEMP_FONTS_DIR / fname)
        print("✅ OTF 下载完成（简体中文 SC）")
        return True
    except Exception as e:
        print(f"❌ 下载失败：{e}")
        return False

def write_typst_template():
    print("📝 生成中文 Typst 模板 …")
    # 关键点：
    # - 使用字体优先列表处理中英文混排
    # - 开启 CJK-Latin 自动间距
    # - 默认等宽字体用系统自带 DejaVu Sans Mono 作为兜底
    template = r"""// Chinese Typst Template (CJK ready)
// 需要先让 Typst 找到 Noto CJK 字体（见脚本输出）

#set text(
  font: (
    (name: "Libertinus Serif", covers: "latin-in-cjk"), // Typst CLI 内置西文字体优先。:contentReference[oaicite:12]{index=12}
    "Noto Serif CJK SC", // 正文中文
  ),
  size: 10pt,
  lang: "zh",
  region: "CN",
  cjk-latin-spacing: auto, // CJK 与 Latin 自动加空隙。:contentReference[oaicite:13]{index=13}
)

#show heading.where(level: 1): it => {
  v(2em, weak: true)
  set text(weight: "bold", size: 1.5em)
  align(center, it)
  v(1em, weak: true)
}

#show raw: it => {
  set text(font: ("JetBrains Mono", "DejaVu Sans Mono"))
  rect(fill: luma(240), stroke: luma(200), inset: 8pt, radius: 4pt, it)
}

#set page(
  paper: "a4",
  margin: (left: 2.5cm, right: 2.5cm, top: 3cm, bottom: 3cm),
  header: align(right)[#datetime.today().display()],
  footer: align(center)[#counter(page).display()],
)

#let report(
  title: "报告标题",
  author: "作者",
  date: datetime.today(),
  body,
) = {
  set document(title: title, author: author)
  align(center)[
    #block(text(weight: "bold", size: 2em, title))
    #v(2em)
    #block(text(size: 1.2em, author))
    #v(1em)
    #block(date.display())
  ]
  #pagebreak()
  body
}
"""
    out = PROJECT_ROOT / "assets" / "template" / "chinese.typ"
    out.parent.mkdir(parents=True, exist_ok=True)
    out.write_text(template, encoding="utf-8")
    print(f"✅ 模板写入 {out}")

def main():
    print("🚀 开始安装与配置 Noto CJK …")
    ensure_dirs()
    install_system_fonts()
    found = try_copy_system_fonts()
    if not found:
        found = download_otf_backup()
        if not found:
            print("❌ 未能获取字体文件，请手动安装 Noto CJK。")
            return 1

    # 为当前进程设置字体路径，便于后续命令演示
    font_paths = f"{FONTS_DIR}:{TEMP_FONTS_DIR}"
    os.environ["TYPST_FONT_PATHS"] = font_paths  # Typst 搜索字体的官方方式之一。:contentReference[oaicite:14]{index=14}
    print(f"🔧 已设置临时环境变量 TYPST_FONT_PATHS={font_paths}")

    write_typst_template()

    # 使用/验证指南（打印提示）
    print("\n🧪 验证步骤：")
    print("1) 列出 Typst 识别到的 CJK 字体：")
    print("   TYPST_FONT_PATHS=\"%s\" typst fonts | grep -i 'CJK\\|Noto'" % font_paths)
    print("2) 生成 vector 工件（typst.ts）：")
    print("   TYPST_FONT_PATHS=\"%s\" typst-ts-cli compile --entry ./demo.typ --format vector" % font_paths)
    print("   # 以上命令来自 typst.ts 的 CLI，用于生成 .artifact.sir.in 文件。")  # :contentReference[oaicite:15]{index=15}
    print("3) 或直接导出 PDF（官方 Typst CLI）：")
    print("   TYPST_FONT_PATHS=\"%s\" typst compile ./demo.typ out.pdf" % font_paths)

    print("\n📌 注意：请避免安装 *-vf 变量字体包，优先使用 TTC/OTF 包。")  # :contentReference[oaicite:16]{index=16}
    print("✅ 完成")
    return 0

if __name__ == "__main__":
    sys.exit(main())
