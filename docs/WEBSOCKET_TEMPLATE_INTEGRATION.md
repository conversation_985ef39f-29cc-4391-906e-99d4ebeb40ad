# WebSocket 模板系统集成指南

## 系统架构

你们的系统已经完美集成了模板功能到 WebSocket 架构中，这是一个非常优雅的设计：

```
前端 WebSocket ←→ WebSocket 路由 ←→ 编译服务 ←→ 模板系统
```

## WebSocket 消息协议

### 1. 连接建立
```javascript
// 连接到 WebSocket
ws = new WebSocket('ws://localhost:8000/api/ws/compile/session_id');

// 接收连接确认
{
  "type": "connection_established",
  "session_id": "session_id",
  "service_ready": true,
  "timestamp": 1234567890
}
```

### 2. 获取模板列表
```javascript
// 发送请求
ws.send(JSON.stringify({
  "type": "list_templates"
}));

// 接收响应
{
  "type": "templates_list",
  "templates": [
    {
      "id": "travel",
      "name": "旅行报告模板",
      "description": "适用于旅行游记、行程报告的简洁模板",
      "preview": "preview/travel.svg",
      "typst_file": "travel/travel.typ"
    }
  ],
  "timestamp": 1234567890
}
```

### 3. 编译文档（带模板）
```javascript
// 发送编译请求
ws.send(JSON.stringify({
  "type": "compile",
  "content": "# 标题\n内容...",
  "template_name": "travel"  // 可选，指定模板
}));

// 接收编译过程消息
{
  "type": "compilation_started",
  "timestamp": 1234567890
}

{
  "type": "compilation_progress",
  "stage": "markdown_to_typst",
  "progress": 0.3,
  "timestamp": 1234567890
}

{
  "type": "compilation_progress",
  "stage": "typst_to_vector",
  "progress": 0.8,
  "timestamp": 1234567890
}

// 接收编译结果
{
  "type": "compilation_complete",
  "result": {
    "vector_data": "base64编码的向量数据",
    "typst_content": "",
    "compilation_time": 0.0,
    "from_cache": false,
    "cache_key": "ws_session_hash"
  },
  "timestamp": 1234567890
}
```

### 4. 错误处理
```javascript
// 模板错误
{
  "type": "templates_error",
  "error": "模板配置文件不存在"
}

// 编译错误
{
  "type": "compilation_error",
  "error": "编译失败的具体原因",
  "timestamp": 1234567890
}
```

### 5. 心跳检测
```javascript
// 发送 ping
ws.send(JSON.stringify({
  "type": "ping"
}));

// 接收 pong
{
  "type": "pong",
  "timestamp": 1234567890
}
```

## 后端实现细节

### WebSocket 路由 (`api/websocket_routes.py`)

#### 连接管理
```python
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self._service_ready = False
```

#### 模板列表处理
```python
async def handle_list_templates_request(session_id: str):
    # 读取 assets/templates/templates.json
    # 返回模板列表给客户端
```

#### 编译请求处理
```python
async def handle_compile_request(message: dict, session_id: str):
    content = message.get('content', '')
    template_name = message.get('template_name', '')
    
    # 调用编译服务
    vector_data = await typst_compile_service.compile_markdown_to_vector_async(
        content, True, template_name
    )
```

### 编译服务 (`api/typst_compile_routes.py`)

#### 模板应用流程
```python
def compile_markdown_to_vector(self, markdown_content: str, template_name: str = ""):
    # 1. Markdown → Typst
    typst_content = self.markdown_service.convert_markdown_to_typst(markdown_content)
    
    # 2. 应用模板（如果指定）
    if template_name:
        typst_content = self.markdown_service.apply_template_to_content(
            template_name, typst_content
        )
    
    # 3. Typst → Vector
    # 编译为向量格式
```

### 模板服务 (`services/markdown_to_typst_service.py`)

#### 简化的模板应用
```python
def apply_template_to_content(self, template_id: str, typst_content: str) -> str:
    # 1. 读取模板配置
    # 2. 构建模板路径
    # 3. 生成 Typst 导入语句
    final_content = f'''#import "{relative_path}": template

#show: template

{typst_content}'''
    return final_content
```

## 前端集成示例

### React/Next.js 集成
```javascript
import { useEffect, useState } from 'react';

function TemplateEditor() {
  const [ws, setWs] = useState(null);
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [content, setContent] = useState('');
  const [compiling, setCompiling] = useState(false);

  useEffect(() => {
    const websocket = new WebSocket('ws://localhost:8000/api/ws/compile/react_session');
    
    websocket.onmessage = (event) => {
      const message = JSON.parse(event.data);
      
      switch (message.type) {
        case 'connection_established':
          // 请求模板列表
          websocket.send(JSON.stringify({ type: 'list_templates' }));
          break;
          
        case 'templates_list':
          setTemplates(message.templates);
          break;
          
        case 'compilation_started':
          setCompiling(true);
          break;
          
        case 'compilation_complete':
          setCompiling(false);
          // 处理编译结果
          handleCompilationResult(message.result);
          break;
          
        case 'compilation_error':
          setCompiling(false);
          console.error('编译失败:', message.error);
          break;
      }
    };
    
    setWs(websocket);
    
    return () => websocket.close();
  }, []);

  const compile = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'compile',
        content: content,
        template_name: selectedTemplate?.id || ''
      }));
    }
  };

  return (
    <div>
      {/* 模板选择器 */}
      <TemplateSelector 
        templates={templates}
        selected={selectedTemplate}
        onSelect={setSelectedTemplate}
      />
      
      {/* 内容编辑器 */}
      <textarea 
        value={content}
        onChange={(e) => setContent(e.target.value)}
        placeholder="输入 Markdown 内容..."
      />
      
      {/* 编译按钮 */}
      <button 
        onClick={compile}
        disabled={compiling || !selectedTemplate}
      >
        {compiling ? '编译中...' : '编译文档'}
      </button>
    </div>
  );
}
```

### Vue.js 集成
```javascript
export default {
  data() {
    return {
      ws: null,
      templates: [],
      selectedTemplate: null,
      content: '',
      compiling: false
    };
  },
  
  mounted() {
    this.connectWebSocket();
  },
  
  methods: {
    connectWebSocket() {
      this.ws = new WebSocket('ws://localhost:8000/api/ws/compile/vue_session');
      
      this.ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      };
    },
    
    handleMessage(message) {
      switch (message.type) {
        case 'templates_list':
          this.templates = message.templates;
          break;
        case 'compilation_complete':
          this.compiling = false;
          this.handleResult(message.result);
          break;
        // ... 其他消息处理
      }
    },
    
    compile() {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.compiling = true;
        this.ws.send(JSON.stringify({
          type: 'compile',
          content: this.content,
          template_name: this.selectedTemplate?.id || ''
        }));
      }
    }
  }
};
```

## 测试工具

### 1. Python 测试脚本
```bash
python scripts/test_websocket_template.py
```

### 2. HTML 测试页面
```bash
# 启动服务器
python main.py

# 在浏览器中打开
open test_websocket_template.html
```

### 3. 命令行测试
```bash
# 使用 websocat 工具测试
echo '{"type":"list_templates"}' | websocat ws://localhost:8000/api/ws/compile/test
```

## 优势总结

### 1. **实时性**
- WebSocket 提供实时双向通信
- 编译进度实时反馈
- 即时错误提示

### 2. **统一接口**
- 所有功能通过一个 WebSocket 连接
- 消息类型清晰，易于扩展
- 前后端协议统一

### 3. **高效性**
- 长连接避免重复握手
- 二进制数据高效传输
- 支持并发编译

### 4. **可扩展性**
- 易于添加新的消息类型
- 支持会话管理
- 便于添加认证和权限控制

这个 WebSocket + 模板的集成方案非常优雅，既保持了系统的实时性，又完美支持了模板功能。
