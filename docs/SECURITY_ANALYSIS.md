# 鉴权完整性分析与改进方案

## 问题1：相对路径最佳实践

### ❌ 原有问题
```python
relative_path = f"../../assets/templates/{template_info['typst_file']}"
```

**问题分析**：
1. **硬编码假设**：假设临时文件总是在 `tmp/typst_work/` 下
2. **脆弱性**：如果目录结构改变，路径就会失效
3. **不直观**：`../../` 不够清晰

### ✅ 最佳解决方案
```python
relative_path = f"assets/templates/{template_info['typst_file']}"
```

**原理说明**：
- `typst-ts-cli` 的工作目录是项目根目录
- 临时文件在 `tmp/typst_work/temp_xxx.typ`
- 模板文件在 `assets/templates/travel/travel.typ`
- 从项目根目录到模板的相对路径就是 `assets/templates/travel/travel.typ`

**验证方式**：
```bash
# 在项目根目录执行
cd /path/to/project
typst-ts-cli compile --entry tmp/typst_work/temp_xxx.typ --format vector
# 临时文件中的导入路径：#import "assets/templates/travel/travel.typ": template
```

## 问题2：鉴权完整性分析

### 🔍 当前鉴权状态

#### ✅ 已实现的安全功能

1. **JWT 认证系统**
   - 访问令牌 (Access Token) + 刷新令牌 (Refresh Token)
   - 令牌自动过期和刷新机制
   - 基于角色的访问控制 (RBAC)

2. **前端安全**
   - HttpOnly Cookies 存储令牌
   - 自动令牌刷新
   - 路由级别的认证检查

3. **后端安全**
   - JWT 签名验证
   - 用户状态检查 (is_active)
   - 速率限制 (Rate Limiting)

#### ❌ 鉴权缺失的关键问题

### 1. **WebSocket 鉴权缺失**

**问题**：WebSocket 路由完全没有鉴权
```python
@router.websocket("/ws/compile/{session_id}")
async def websocket_compile_endpoint(websocket: WebSocket, session_id: str):
    # ❌ 没有任何鉴权检查
    await manager.connect(websocket, session_id)
```

**风险**：
- 任何人都可以连接 WebSocket
- 可以无限制地使用编译服务
- 可能导致资源滥用和 DoS 攻击

### 2. **REST API 鉴权不一致**

**问题**：部分 API 路由缺少鉴权
```python
# word_format_routes.py 中注释掉了认证
# from core.security import get_current_user, require_roles
```

### 3. **资源访问控制缺失**

**问题**：
- 没有用户级别的资源隔离
- 所有用户可以访问所有模板
- 没有使用配额限制

## 🔧 完整的鉴权改进方案

### 1. WebSocket 鉴权实现

#### 方案A：连接时鉴权（推荐）
```python
@router.websocket("/ws/compile/{session_id}")
async def websocket_compile_endpoint(websocket: WebSocket, session_id: str):
    try:
        # 鉴权检查
        user_id = await get_current_user_ws(websocket)
        logger.info(f"WebSocket 连接鉴权成功: user_id={user_id}, session={session_id}")

        await manager.connect(websocket, session_id, user_id)

        # 后续消息处理...
    except WebSocketException as e:
        logger.warning(f"WebSocket 鉴权失败: {e.reason}")
        await websocket.close(code=e.code, reason=e.reason)
        return
```

#### 方案B：消息级鉴权
```python
async def handle_compile_request(message: dict, session_id: str, user_id: str):
    # 检查用户编译配额
    if not await check_user_quota(user_id):
        await manager.send_personal_message({
            'type': 'compilation_error',
            'error': '编译配额已用完，请升级账户'
        }, session_id)
        return

    # 记录用户使用情况
    await log_user_activity(user_id, 'compile', len(message.get('content', '')))
```

### 2. REST API 鉴权统一

#### 模板 API 鉴权
```python
@router.get("/templates", dependencies=[Depends(get_current_user)])
async def list_templates(current_user: User = Depends(get_current_user)):
    # 根据用户角色返回可用模板
    if current_user.role == "premium":
        return all_templates
    else:
        return basic_templates
```

#### 编译 API 鉴权
```python
@router.post("/compile", dependencies=[Depends(get_current_user)])
async def compile_document(
    request: CompileRequest,
    current_user: User = Depends(get_current_user)
):
    # 检查用户权限和配额
    await check_compile_permission(current_user, request)
```

### 3. 资源访问控制

#### 用户配额系统
```python
class UserQuota:
    def __init__(self, user: User):
        self.user = user
        self.daily_limit = self._get_daily_limit()
        self.used_today = self._get_usage_today()

    def _get_daily_limit(self) -> int:
        limits = {
            "free": 10,
            "basic": 100,
            "premium": 1000,
            "admin": float('inf')
        }
        return limits.get(self.user.role, 10)

    async def can_compile(self) -> bool:
        return self.used_today < self.daily_limit

    async def record_usage(self, content_length: int):
        # 记录使用情况到数据库
        pass
```

#### 模板权限控制
```python
class TemplatePermission:
    @staticmethod
    def get_available_templates(user: User) -> List[str]:
        if user.role in ["premium", "admin"]:
            return ["travel", "academic", "business", "simple"]
        elif user.role == "basic":
            return ["travel", "simple"]
        else:  # free
            return ["simple"]

    @staticmethod
    def can_use_template(user: User, template_id: str) -> bool:
        available = TemplatePermission.get_available_templates(user)
        return template_id in available
```

### 4. 安全增强措施

#### IP 白名单和黑名单
```python
class SecurityMiddleware:
    def __init__(self):
        self.blocked_ips = set()
        self.rate_limits = {}

    async def check_ip_security(self, request: Request) -> bool:
        client_ip = request.client.host

        # 检查黑名单
        if client_ip in self.blocked_ips:
            raise HTTPException(status_code=403, detail="IP blocked")

        # 检查速率限制
        if await self._is_rate_limited(client_ip):
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

        return True
```

#### 内容安全检查
```python
class ContentSecurity:
    @staticmethod
    def validate_markdown_content(content: str) -> bool:
        # 检查内容长度
        if len(content) > 100000:  # 100KB 限制
            raise HTTPException(status_code=413, detail="Content too large")

        # 检查恶意内容
        dangerous_patterns = [
            r'#import\s+"[^"]*\.\./.*"',  # 路径遍历
            r'#read\s*\(',  # 文件读取
            r'#eval\s*\(',  # 代码执行
        ]

        for pattern in dangerous_patterns:
            if re.search(pattern, content):
                raise HTTPException(status_code=400, detail="Dangerous content detected")

        return True
```

### 5. 审计和监控

#### 操作日志
```python
class AuditLogger:
    @staticmethod
    async def log_user_action(
        user_id: str,
        action: str,
        resource: str,
        details: dict = None
    ):
        log_entry = {
            "timestamp": datetime.utcnow(),
            "user_id": user_id,
            "action": action,
            "resource": resource,
            "details": details or {},
            "ip_address": request.client.host if request else None
        }

        # 写入数据库或日志文件
        await save_audit_log(log_entry)
```

#### 异常监控
```python
class SecurityMonitor:
    @staticmethod
    async def detect_anomalies(user_id: str, action: str):
        # 检测异常行为
        recent_actions = await get_recent_actions(user_id, minutes=5)

        if len(recent_actions) > 50:  # 5分钟内超过50次操作
            await alert_security_team(f"User {user_id} showing suspicious activity")
            await temporarily_block_user(user_id)
```

## 🚀 实施优先级

### 高优先级（立即实施）
1. **WebSocket 鉴权**：防止未授权访问
2. **内容安全检查**：防止恶意内容注入
3. **基础配额限制**：防止资源滥用

### 中优先级（1-2周内）
1. **模板权限控制**：实现分级访问
2. **操作审计日志**：追踪用户行为
3. **IP 安全检查**：防止恶意 IP

### 低优先级（长期优化）
1. **高级异常检测**：AI 驱动的安全监控
2. **细粒度权限控制**：更复杂的 RBAC
3. **安全仪表板**：可视化安全状态

## 📋 实施检查清单

- [ ] 实现 WebSocket 鉴权
- [ ] 添加内容安全验证
- [ ] 实现用户配额系统
- [ ] 添加模板权限控制
- [ ] 实现操作审计日志
- [ ] 添加 IP 安全检查
- [ ] 创建安全测试用例
- [ ] 更新安全文档
- [ ] 进行安全渗透测试
